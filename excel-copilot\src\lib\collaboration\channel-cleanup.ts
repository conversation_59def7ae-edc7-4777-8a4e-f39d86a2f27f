import { createClient } from '@supabase/supabase-js';
import { logger } from '@/lib/logger';
import { persistentCollaborationStore } from './persistent-store';

/**
 * Sistema de cleanup automático de channels Supabase
 * Remove channels inativos e limpa recursos não utilizados
 */

export interface ChannelInfo {
  channelName: string;
  workbookId: string;
  lastActivity: number;
  subscriberCount: number;
  isActive: boolean;
}

export class SupabaseChannelCleanup {
  private supabaseClient;
  private readonly CLEANUP_INTERVAL = 30 * 60 * 1000; // 30 minutos
  private readonly INACTIVE_THRESHOLD = 60 * 60 * 1000; // 1 hora
  private readonly MAX_INACTIVE_CHANNELS = 100;
  private cleanupTimer: NodeJS.Timeout | null = null;
  private activeChannels: Map<string, ChannelInfo> = new Map();

  constructor() {
    // Inicializar cliente Supabase
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
    const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

    if (!supabaseUrl || !supabaseKey) {
      logger.warn('Supabase não configurado para cleanup de channels');
      return;
    }

    this.supabaseClient = createClient(supabaseUrl, supabaseKey);
    this.startCleanupScheduler();

    logger.info('Sistema de cleanup de channels Supabase inicializado');
  }

  /**
   * Inicia o agendador de limpeza
   */
  private startCleanupScheduler(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
    }

    this.cleanupTimer = setInterval(() => {
      this.performCleanup();
    }, this.CLEANUP_INTERVAL);

    // Executar limpeza inicial após 5 minutos
    setTimeout(() => {
      this.performCleanup();
    }, 5 * 60 * 1000);
  }

  /**
   * Para o agendador de limpeza
   */
  public stopCleanupScheduler(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
      this.cleanupTimer = null;
    }
  }

  /**
   * Registra atividade em um channel
   */
  public registerChannelActivity(workbookId: string): void {
    const channelName = `workbook:${workbookId}`;
    const now = Date.now();

    const channelInfo: ChannelInfo = {
      channelName,
      workbookId,
      lastActivity: now,
      subscriberCount: 0, // Será atualizado durante a limpeza
      isActive: true,
    };

    this.activeChannels.set(channelName, channelInfo);
  }

  /**
   * Remove registro de um channel
   */
  public unregisterChannel(workbookId: string): void {
    const channelName = `workbook:${workbookId}`;
    this.activeChannels.delete(channelName);
  }

  /**
   * Executa limpeza de channels inativos
   */
  private async performCleanup(): Promise<void> {
    try {
      logger.info('Iniciando limpeza de channels Supabase');

      const now = Date.now();
      const inactiveThreshold = now - this.INACTIVE_THRESHOLD;
      let cleanedChannels = 0;
      let totalChannels = 0;

      // Obter lista de planilhas ativas do store persistente
      const activeWorkbooks = await persistentCollaborationStore.listActiveWorkbooks();
      
      // Verificar channels registrados
      for (const [channelName, channelInfo] of this.activeChannels.entries()) {
        totalChannels++;

        // Verificar se channel está inativo
        if (channelInfo.lastActivity < inactiveThreshold) {
          // Verificar se ainda há colaboradores ativos
          const hasActiveCollaborators = activeWorkbooks.includes(channelInfo.workbookId);
          
          if (!hasActiveCollaborators) {
            await this.cleanupChannel(channelInfo);
            this.activeChannels.delete(channelName);
            cleanedChannels++;
          }
        }
      }

      // Limpar channels órfãos (não registrados mas potencialmente ativos)
      await this.cleanupOrphanChannels(activeWorkbooks);

      // Limitar número de channels inativos mantidos em memória
      if (this.activeChannels.size > this.MAX_INACTIVE_CHANNELS) {
        await this.pruneInactiveChannels();
      }

      logger.info('Limpeza de channels concluída', {
        totalChannels,
        channelsLimpos: cleanedChannels,
        channelsAtivos: this.activeChannels.size,
      });
    } catch (error) {
      logger.error('Erro na limpeza de channels', {
        error: error instanceof Error ? error.message : 'Erro desconhecido',
      });
    }
  }

  /**
   * Limpa um channel específico
   */
  private async cleanupChannel(channelInfo: ChannelInfo): Promise<void> {
    try {
      // Tentar desconectar do channel (se ainda conectado)
      const channel = this.supabaseClient?.channel(channelInfo.channelName);
      
      if (channel) {
        await channel.unsubscribe();
        logger.debug('Channel desconectado', {
          channelName: channelInfo.channelName,
          workbookId: channelInfo.workbookId,
        });
      }

      // Remover estado persistente se não há mais atividade
      await persistentCollaborationStore.removeCollaborationState(channelInfo.workbookId);

    } catch (error) {
      logger.warn('Erro ao limpar channel específico', {
        channelName: channelInfo.channelName,
        error: error instanceof Error ? error.message : 'Erro desconhecido',
      });
    }
  }

  /**
   * Limpa channels órfãos
   */
  private async cleanupOrphanChannels(activeWorkbooks: string[]): Promise<void> {
    try {
      // Esta é uma implementação simplificada
      // Em uma implementação real, seria necessário acessar a lista de channels ativos do Supabase
      // Por limitações da API pública, fazemos limpeza baseada no nosso registro local

      const registeredWorkbooks = Array.from(this.activeChannels.values())
        .map(channel => channel.workbookId);

      // Identificar workbooks que não estão mais ativos
      const orphanWorkbooks = registeredWorkbooks.filter(
        workbookId => !activeWorkbooks.includes(workbookId)
      );

      for (const workbookId of orphanWorkbooks) {
        const channelName = `workbook:${workbookId}`;
        const channelInfo = this.activeChannels.get(channelName);
        
        if (channelInfo) {
          await this.cleanupChannel(channelInfo);
          this.activeChannels.delete(channelName);
        }
      }

      if (orphanWorkbooks.length > 0) {
        logger.info('Channels órfãos limpos', {
          channelsOrfaos: orphanWorkbooks.length,
        });
      }
    } catch (error) {
      logger.error('Erro na limpeza de channels órfãos', {
        error: error instanceof Error ? error.message : 'Erro desconhecido',
      });
    }
  }

  /**
   * Remove channels inativos em excesso
   */
  private async pruneInactiveChannels(): Promise<void> {
    const channelEntries = Array.from(this.activeChannels.entries());
    
    // Ordenar por última atividade (mais antigos primeiro)
    channelEntries.sort((a, b) => a[1].lastActivity - b[1].lastActivity);

    // Remover channels mais antigos que excedem o limite
    const excessCount = channelEntries.length - this.MAX_INACTIVE_CHANNELS;
    const channelsToRemove = channelEntries.slice(0, excessCount);

    for (const [channelName, channelInfo] of channelsToRemove) {
      await this.cleanupChannel(channelInfo);
      this.activeChannels.delete(channelName);
    }

    if (channelsToRemove.length > 0) {
      logger.info('Channels inativos em excesso removidos', {
        channelsRemovidos: channelsToRemove.length,
      });
    }
  }

  /**
   * Obtém estatísticas de channels
   */
  public getChannelStats(): {
    totalChannels: number;
    activeChannels: number;
    inactiveChannels: number;
    oldestChannelAge: number;
  } {
    const now = Date.now();
    const inactiveThreshold = now - this.INACTIVE_THRESHOLD;
    
    let activeCount = 0;
    let inactiveCount = 0;
    let oldestAge = 0;

    for (const channelInfo of this.activeChannels.values()) {
      if (channelInfo.lastActivity > inactiveThreshold) {
        activeCount++;
      } else {
        inactiveCount++;
      }

      const age = now - channelInfo.lastActivity;
      if (age > oldestAge) {
        oldestAge = age;
      }
    }

    return {
      totalChannels: this.activeChannels.size,
      activeChannels: activeCount,
      inactiveChannels: inactiveCount,
      oldestChannelAge: oldestAge,
    };
  }

  /**
   * Força limpeza imediata
   */
  public async forceCleanup(): Promise<void> {
    await this.performCleanup();
  }

  /**
   * Verifica saúde do sistema de cleanup
   */
  public healthCheck(): {
    isRunning: boolean;
    nextCleanup: number;
    channelCount: number;
  } {
    return {
      isRunning: this.cleanupTimer !== null,
      nextCleanup: this.cleanupTimer ? Date.now() + this.CLEANUP_INTERVAL : 0,
      channelCount: this.activeChannels.size,
    };
  }
}

// Instância singleton do sistema de cleanup
export const channelCleanup = new SupabaseChannelCleanup();
