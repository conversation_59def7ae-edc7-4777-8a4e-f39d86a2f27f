import { Redis } from '@upstash/redis';
import { logger } from '@/lib/logger';
import { ActiveCollaborator } from '@/lib/collaboration/store';

/**
 * Sistema de persistência para store de colaboração
 * Usa Redis/Upstash para manter estado entre restarts do servidor
 */

export interface CollaborationState {
  workbookId: string;
  collaborators: ActiveCollaborator[];
  lastActivity: number;
  activeConnections: number;
}

export interface PersistentCollaborationData {
  [workbookId: string]: CollaborationState;
}

export class PersistentCollaborationStore {
  private redis: Redis | null = null;
  private readonly REDIS_PREFIX = 'collaboration:';
  private readonly EXPIRY_TIME = 24 * 60 * 60; // 24 horas em segundos
  private readonly CLEANUP_INTERVAL = 60 * 60 * 1000; // 1 hora em ms
  private memoryFallback: Map<string, CollaborationState> = new Map();

  constructor() {
    // Inicializar Redis se disponível
    if (process.env.UPSTASH_REDIS_REST_URL && process.env.UPSTASH_REDIS_REST_TOKEN) {
      this.redis = new Redis({
        url: process.env.UPSTASH_REDIS_REST_URL,
        token: process.env.UPSTASH_REDIS_REST_TOKEN,
      });
      logger.info('Redis configurado para persistência de colaboração');
    } else {
      logger.warn('Redis não configurado, usando fallback em memória');
    }

    // Limpeza periódica
    setInterval(() => {
      this.cleanupExpiredStates();
    }, this.CLEANUP_INTERVAL);
  }

  /**
   * Salva estado de colaboração
   */
  async saveCollaborationState(
    workbookId: string,
    collaborators: ActiveCollaborator[]
  ): Promise<void> {
    const state: CollaborationState = {
      workbookId,
      collaborators,
      lastActivity: Date.now(),
      activeConnections: collaborators.length,
    };

    try {
      if (this.redis) {
        const key = `${this.REDIS_PREFIX}${workbookId}`;
        await this.redis.setex(key, this.EXPIRY_TIME, JSON.stringify(state));
      } else {
        // Fallback para memória
        this.memoryFallback.set(workbookId, state);
      }

      logger.debug('Estado de colaboração salvo', {
        workbookId,
        collaboratorsCount: collaborators.length,
      });
    } catch (error) {
      logger.error('Erro ao salvar estado de colaboração', {
        workbookId,
        error: error instanceof Error ? error.message : 'Erro desconhecido',
      });
      
      // Fallback para memória em caso de erro
      this.memoryFallback.set(workbookId, state);
    }
  }

  /**
   * Carrega estado de colaboração
   */
  async loadCollaborationState(workbookId: string): Promise<CollaborationState | null> {
    try {
      if (this.redis) {
        const key = `${this.REDIS_PREFIX}${workbookId}`;
        const data = await this.redis.get(key);
        
        if (data && typeof data === 'string') {
          const state = JSON.parse(data) as CollaborationState;
          logger.debug('Estado de colaboração carregado do Redis', {
            workbookId,
            collaboratorsCount: state.collaborators.length,
          });
          return state;
        }
      } else {
        // Fallback para memória
        const state = this.memoryFallback.get(workbookId);
        if (state) {
          logger.debug('Estado de colaboração carregado da memória', {
            workbookId,
            collaboratorsCount: state.collaborators.length,
          });
          return state;
        }
      }

      return null;
    } catch (error) {
      logger.error('Erro ao carregar estado de colaboração', {
        workbookId,
        error: error instanceof Error ? error.message : 'Erro desconhecido',
      });

      // Tentar fallback para memória
      const state = this.memoryFallback.get(workbookId);
      return state || null;
    }
  }

  /**
   * Remove estado de colaboração
   */
  async removeCollaborationState(workbookId: string): Promise<void> {
    try {
      if (this.redis) {
        const key = `${this.REDIS_PREFIX}${workbookId}`;
        await this.redis.del(key);
      }
      
      // Remover também da memória
      this.memoryFallback.delete(workbookId);

      logger.debug('Estado de colaboração removido', { workbookId });
    } catch (error) {
      logger.error('Erro ao remover estado de colaboração', {
        workbookId,
        error: error instanceof Error ? error.message : 'Erro desconhecido',
      });
    }
  }

  /**
   * Lista todas as planilhas com colaboração ativa
   */
  async listActiveWorkbooks(): Promise<string[]> {
    try {
      const workbooks: string[] = [];

      if (this.redis) {
        const pattern = `${this.REDIS_PREFIX}*`;
        const keys = await this.redis.keys(pattern);
        
        for (const key of keys) {
          const workbookId = key.replace(this.REDIS_PREFIX, '');
          workbooks.push(workbookId);
        }
      }

      // Adicionar também da memória
      for (const workbookId of this.memoryFallback.keys()) {
        if (!workbooks.includes(workbookId)) {
          workbooks.push(workbookId);
        }
      }

      return workbooks;
    } catch (error) {
      logger.error('Erro ao listar planilhas ativas', {
        error: error instanceof Error ? error.message : 'Erro desconhecido',
      });

      // Fallback apenas para memória
      return Array.from(this.memoryFallback.keys());
    }
  }

  /**
   * Obtém estatísticas de colaboração
   */
  async getCollaborationStats(): Promise<{
    totalWorkbooks: number;
    totalCollaborators: number;
    averageCollaboratorsPerWorkbook: number;
  }> {
    try {
      const workbooks = await this.listActiveWorkbooks();
      let totalCollaborators = 0;

      for (const workbookId of workbooks) {
        const state = await this.loadCollaborationState(workbookId);
        if (state) {
          totalCollaborators += state.collaborators.length;
        }
      }

      return {
        totalWorkbooks: workbooks.length,
        totalCollaborators,
        averageCollaboratorsPerWorkbook: workbooks.length > 0 
          ? Math.round((totalCollaborators / workbooks.length) * 100) / 100 
          : 0,
      };
    } catch (error) {
      logger.error('Erro ao obter estatísticas de colaboração', {
        error: error instanceof Error ? error.message : 'Erro desconhecido',
      });

      return {
        totalWorkbooks: 0,
        totalCollaborators: 0,
        averageCollaboratorsPerWorkbook: 0,
      };
    }
  }

  /**
   * Atualiza timestamp de atividade
   */
  async updateActivity(workbookId: string): Promise<void> {
    try {
      const state = await this.loadCollaborationState(workbookId);
      if (state) {
        state.lastActivity = Date.now();
        await this.saveCollaborationState(workbookId, state.collaborators);
      }
    } catch (error) {
      logger.error('Erro ao atualizar atividade', {
        workbookId,
        error: error instanceof Error ? error.message : 'Erro desconhecido',
      });
    }
  }

  /**
   * Limpa estados expirados
   */
  private async cleanupExpiredStates(): Promise<void> {
    try {
      const now = Date.now();
      const expiryThreshold = now - (this.EXPIRY_TIME * 1000);
      let cleanedCount = 0;

      // Limpeza da memória
      for (const [workbookId, state] of this.memoryFallback.entries()) {
        if (state.lastActivity < expiryThreshold) {
          this.memoryFallback.delete(workbookId);
          cleanedCount++;
        }
      }

      // Redis tem TTL automático, mas vamos verificar estados órfãos
      if (this.redis) {
        const workbooks = await this.listActiveWorkbooks();
        for (const workbookId of workbooks) {
          const state = await this.loadCollaborationState(workbookId);
          if (state && state.lastActivity < expiryThreshold) {
            await this.removeCollaborationState(workbookId);
            cleanedCount++;
          }
        }
      }

      if (cleanedCount > 0) {
        logger.info('Limpeza de estados de colaboração realizada', {
          estadosRemovidos: cleanedCount,
        });
      }
    } catch (error) {
      logger.error('Erro na limpeza de estados expirados', {
        error: error instanceof Error ? error.message : 'Erro desconhecido',
      });
    }
  }

  /**
   * Verifica saúde da conexão Redis
   */
  async healthCheck(): Promise<{ redis: boolean; memory: boolean }> {
    const health = { redis: false, memory: true };

    try {
      if (this.redis) {
        // Teste simples de ping
        await this.redis.ping();
        health.redis = true;
      }
    } catch (error) {
      logger.warn('Redis não disponível para colaboração', {
        error: error instanceof Error ? error.message : 'Erro desconhecido',
      });
    }

    return health;
  }
}

// Instância singleton do store persistente
export const persistentCollaborationStore = new PersistentCollaborationStore();
